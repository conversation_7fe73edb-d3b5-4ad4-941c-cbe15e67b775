# 主域名配置文件
# liangliangdamowang.edu.deal

# upstream定义
upstream new-api {
    server new-api:3000;
    keepalive 32;
}

# 限流区域定义
limit_req_zone $binary_remote_addr zone=general:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=api:10m rate=5r/s;
limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;

# WebSocket连接升级映射
map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name liangliangdamowang.edu.deal;
    
    # 访问日志
    access_log /var/log/nginx/access/liangliangdamowang.edu.deal-http.log main;
    
    # 强制重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

# HTTPS主服务器
server {
    listen 443 ssl;
    http2 on;
    server_name liangliangdamowang.edu.deal;
    
    # SSL证书配置
    ssl_certificate /etc/nginx/ssl/certificate.crt;
    ssl_certificate_key /etc/nginx/ssl/private.key;

    # 包含SSL通用配置
    include /etc/nginx/includes/ssl-common.conf;

    # 包含安全头配置
    include /etc/nginx/includes/security-headers.conf;

    # 包含访问控制配置
    include /etc/nginx/includes/access-control.conf;
    
    # 访问日志
    access_log /var/log/nginx/access/liangliangdamowang.edu.deal.log main;
    error_log /var/log/nginx/error/liangliangdamowang.edu.deal.log warn;
    
    # 根路径（主站内容）
    location / {
        root /var/www/html;
        index index.html index.htm;
        try_files $uri $uri/ =404;
        
        # 应用限流
        limit_req zone=general burst=20 nodelay;
        limit_conn conn_limit_per_ip 20;
    }
    
    # New-API路径配置 - 核心子路径访问
    location /ai/ {
        # 包含代理通用配置
        include /etc/nginx/includes/proxy-common.conf;

        # 移除/ai前缀，转发到new-api容器
        rewrite ^/ai/(.*)$ /$1 break;

        # 代理到本地的new-api服务
        proxy_pass http://new-api/;

        # 设置子路径前缀头，让new-api知道它运行在子路径下
        proxy_set_header X-Forwarded-Prefix /ai;

        # API限流
        limit_req zone=api burst=10 nodelay;
        limit_conn conn_limit_per_ip 10;

        # 特殊处理登录接口
        location /ai/api/user/login {
            include /etc/nginx/includes/proxy-common.conf;
            rewrite ^/ai/(.*)$ /$1 break;
            proxy_pass http://new-api;
            proxy_set_header X-Forwarded-Prefix /ai;
            limit_req zone=login burst=3 nodelay;
        }

        # 特殊处理静态资源
        location ~* /ai/.*\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            include /etc/nginx/includes/proxy-common.conf;
            rewrite ^/ai/(.*)$ /$1 break;
            proxy_pass http://new-api;
            proxy_set_header X-Forwarded-Prefix /ai;
            expires 1d;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # 处理/ai重定向
    location = /ai {
        return 301 /ai/;
    }
    
    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
